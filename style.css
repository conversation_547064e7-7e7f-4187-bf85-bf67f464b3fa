/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/*start-project/*

/* Document
   ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */

html {
  line-height: 1.15; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* Sections
     ========================================================================== */

/**
   * Remove the margin in all browsers.
   */

body {
  margin: 0;
}

/**
   * Render the `main` element consistently in IE.
   */

main {
  display: block;
}

/**
   * Correct the font size and margin on `h1` elements within `section` and
   * `article` contexts in Chrome, Firefox, and Safari.
   */

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
     ========================================================================== */

/**
   * 1. Add the correct box sizing in Firefox.
   * 2. Show the overflow in Edge and IE.
   */

hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */

pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/* Text-level semantics
     ========================================================================== */

/**
   * Remove the gray background on active links in IE 10.
   */

a {
  background-color: transparent;
}

/**
   * 1. Remove the bottom border in Chrome 57-
   * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
   */

abbr[title] {
  border-bottom: none; /* 1 */
  text-decoration: underline; /* 2 */
  text-decoration: underline dotted; /* 2 */
}

/**
   * Add the correct font weight in Chrome, Edge, and Safari.
   */

b,
strong {
  font-weight: bolder;
}

/**
   * 1. Correct the inheritance and scaling of font size in all browsers.
   * 2. Correct the odd `em` font sizing in all browsers.
   */

code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
   * Add the correct font size in all browsers.
   */

small {
  font-size: 80%;
}

/**
   * Prevent `sub` and `sup` elements from affecting the line height in
   * all browsers.
   */

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
     ========================================================================== */

/**
   * Remove the border on images inside links in IE 10.
   */

img {
  border-style: none;
}

/* Forms
     ========================================================================== */

/**
   * 1. Change the font styles in all browsers.
   * 2. Remove the margin in Firefox and Safari.
   */

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
   * Show the overflow in IE.
   * 1. Show the overflow in Edge.
   */

button,
input {
  /* 1 */
  overflow: visible;
}

/**
   * Remove the inheritance of text transform in Edge, Firefox, and IE.
   * 1. Remove the inheritance of text transform in Firefox.
   */

button,
select {
  /* 1 */
  text-transform: none;
}

/**
   * Correct the inability to style clickable types in iOS and Safari.
   */

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

/**
   * Remove the inner border and padding in Firefox.
   */

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
   * Restore the focus styles unset by the previous rule.
   */

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
   * Correct the padding in Firefox.
   */

fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
   * 1. Correct the text wrapping in Edge and IE.
   * 2. Correct the color inheritance from `fieldset` elements in IE.
   * 3. Remove the padding so developers are not caught out when they zero out
   *    `fieldset` elements in all browsers.
   */

legend {
  box-sizing: border-box; /* 1 */
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  padding: 0; /* 3 */
  white-space: normal; /* 1 */
}

/**
   * Add the correct vertical alignment in Chrome, Firefox, and Opera.
   */

progress {
  vertical-align: baseline;
}

/**
   * Remove the default vertical scrollbar in IE 10+.
   */

textarea {
  overflow: auto;
}

/**
   * 1. Add the correct box sizing in IE 10.
   * 2. Remove the padding in IE 10.
   */

[type="checkbox"],
[type="radio"] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
   * Correct the cursor style of increment and decrement buttons in Chrome.
   */

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
   * 1. Correct the odd appearance in Chrome and Safari.
   * 2. Correct the outline style in Safari.
   */

[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
   * Remove the inner padding in Chrome and Safari on macOS.
   */

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
   * 1. Correct the inability to style clickable types in iOS and Safari.
   * 2. Change font properties to `inherit` in Safari.
   */

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* Interactive
     ========================================================================== */

/*
   * Add the correct display in Edge, IE 10+, and Firefox.
   */

details {
  display: block;
}

/*
   * Add the correct display in all browsers.
   */

summary {
  display: list-item;
}

/* Misc
     ========================================================================== */

/**
   * Add the correct display in IE 10+.
   */

template {
  display: none;
}

/**
   * Add the correct display in IE 10.
   */

[hidden] {
  display: none;
}

/*------------------- start-project -----------------*/
html {
  box-sizing: border-box;
}
*,
*::before,
*::after {
  box-sizing: inherit;
}
h1,
h2,
h3,
h4,
h5,
p,
ul,
li {
  margin: 0;
  padding: 0;
}
body {
  font-family: "Open Sans", sans-serif;
}
a {
  text-decoration: none;
}
li {
  list-style: none;
}

/*--------------------- header-top ---------------------*/
.container {
  max-width: 70rem;
  margin: 0 auto;
}
.header-top-wrapper {
  background-color: #333745;
}
.header-info {
  /* background-color: #333745; */
  color: #fff;
  padding: 11px 0px;
  display: flex;
  justify-content: space-between;
  font-size: 0.7rem;
  font-weight: 300;
}
.header-info-contact {
  display: flex;
}
.header-info__email {
  color: #f2d03b;
}
.header-info-social {
  display: flex;
}
.header-info__text,
.header-info__icon {
  padding: 0 5px;
}

/******************** nav ******************/
.nav {
  display: flex;
  justify-content: space-between;
}
.menu {
  display: flex;
}
.nav-content {
  display: flex;
  align-items: center;
}
.nav-logo {
  max-width: 8rem;
}
.nav-logo__image {
  width: 100%;
}
.menu__link {
  display: inline-block;
  padding: 30px 20px;
  color: #898989;
  font-size: 0.9rem;
  text-transform: uppercase;
}
.menu__link:hover .menu__text {
  color: #f2d03b;
  border-bottom: 2px solid #f2d03b;
}
.menu__text {
  border-bottom: 2px solid transparent;
  transition: all 400ms;
}
.nav-search i {
  color: #898989;
}
.nav-search:hover i {
  color: #333745;
  cursor: pointer;
}
.active .menu__text {
  color: #f2d03b;
}

/*--------------------- qode-home-slider-5 ---------------------*/
.qode-home-slider-5 {
  position: relative;
  width: 100%;
  height: 508px;
  overflow: hidden;
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  display: flex;
  align-items: center;
}

.slide.active {
  opacity: 1;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.slide-content {
  position: relative;
  z-index: 2;
  max-width: 500px;
  width: 100%;
}

/* First slide - content on the LEFT */
.slide:nth-child(1) {
  justify-content: flex-start;
}

.slide:nth-child(1) .slide-content {
  text-align: left;
  margin-left: 226px;
  margin-right: auto;
}

/* Second slide - content on the RIGHT */
.slide:nth-child(2) {
  justify-content: flex-end;
}

.slide:nth-child(2) .slide-content {
  text-align: right;
  margin-left: auto;
  margin-right: 300px;
}

.slide-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 2rem;
}

.slide-description {
  line-height: 1.8;
  margin-bottom: 3rem;
  font-weight: 300;
  max-width: 450px;
  opacity: 0.9;
  color: #5c5c5c;
}

/* Description alignment for each slide */
.slide:nth-child(1) .slide-description {
  margin-left: 0;
  margin-right: auto;
}

.slide:nth-child(2) .slide-description {
  margin-left: auto;
  margin-right: 0;
}

.slide-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* First slide - buttons aligned LEFT */
.slide:nth-child(1) .slide-buttons {
  justify-content: flex-start;
}

/* Second slide - buttons aligned RIGHT */
.slide:nth-child(2) .slide-buttons {
  justify-content: flex-end;
}

.btn {
  display: inline-block;
  padding: 15px 35px;
  text-decoration: none;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 0.8rem;
  letter-spacing: 2px;
  border: none;
  transition: all 0.4s ease;
  cursor: pointer;
  font-family: "Open Sans", sans-serif;
}

.btn-primary {
  background-color: #f2d03b;
  color: #333;
  border-radius: 5px;
}

.btn-primary:hover {
  background-color: transparent;
  color: black;
}

.btn-secondary {
  background-color: #2f2f2f;
  color: white;
  border-radius: 5px;
}

.btn-secondary:hover {
  background-color: white;
  color: #333;
}

.slider-navigation {
  position: absolute;
  bottom: 50px;
  left: 0;
  right: 0;
  z-index: 3;
}

.nav-dots {
  display: flex;
  justify-content: center;
  gap: 3rem;
}

.nav-dot {
  width: 10px;
  height: 10px;
  background-color: black;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 1px;
  position: relative;
}

.nav-dot.active {
  background-color: white;
}

.nav-dot:hover {
  color: rgba(255, 255, 255, 0.8);
}

.nav-dot.active::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 1px;
  background-color: #f2d03b;
}

/* Responsive Design for Slider */
@media (max-width: 1024px) {
  .slide:nth-child(1) .slide-content {
    margin-left: 80px;
  }

  .slide:nth-child(2) .slide-content {
    margin-right: 80px;
  }

  .slide-title {
    font-size: 3.5rem;
  }
}

@media (max-width: 768px) {
  .slide:nth-child(1),
  .slide:nth-child(2) {
    justify-content: center;
  }

  .slide:nth-child(1) .slide-content,
  .slide:nth-child(2) .slide-content {
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    max-width: 90%;
  }

  .slide:nth-child(1) .slide-description,
  .slide:nth-child(2) .slide-description {
    margin-left: auto;
    margin-right: auto;
  }

  .slide-title {
    font-size: 2.8rem;
    margin-bottom: 1.5rem;
    letter-spacing: 2px;
  }

  .slide-description {
    font-size: 0.95rem;
    margin-bottom: 2.5rem;
    max-width: 100%;
  }

  .slide-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .slide:nth-child(1) .slide-buttons,
  .slide:nth-child(2) .slide-buttons {
    justify-content: center;
  }

  .btn {
    padding: 12px 30px;
    font-size: 0.75rem;
  }

  .nav-dots {
    gap: 2rem;
  }

  .slider-navigation {
    bottom: 30px;
  }
}

@media (max-width: 480px) {
  .slide-title {
    font-size: 2.2rem;
    letter-spacing: 1px;
    margin-bottom: 1rem;
  }

  .slide-description {
    font-size: 0.9rem;
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .slide:nth-child(1) .slide-content,
  .slide:nth-child(2) .slide-content {
    max-width: 95%;
    padding: 0 20px;
  }

  .btn {
    padding: 10px 25px;
    font-size: 0.7rem;
    letter-spacing: 1px;
  }

  .nav-dots {
    gap: 1.5rem;
  }

  .nav-dot {
    font-size: 0.75rem;
  }

  .slider-navigation {
    bottom: 25px;
  }
}

/*--------------------- Services Section ---------------------*/
.services-section {
  padding: 80px 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.service-item {
  text-align: center;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-icon {
  margin-bottom: 25px;
  background-color: #f9fafa;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-icon i {
  font-size: 25px;
  color: #272727;
  transition: color 0.3s ease;
}

.service-item:hover .service-icon i {
  color: #333745;
}

.service-title {
  font-size: 1rem;
  font-weight: 300;
  margin-bottom: 15px;
  color: #000;
  letter-spacing: 1px;
}

.service-description {
  font-size: 13px;
  line-height: 1.9;
  color: #959595;
  font-weight: 300;
}

/* Services Responsive */
@media (max-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .services-section {
    padding: 60px 0;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .service-item {
    padding: 30px 15px;
  }

  .service-icon i {
    font-size: 2.5rem;
  }

  .service-title {
    font-size: 1.1rem;
  }
}

/*--------------------- About Section ---------------------*/
.about-section {
  padding: 36px 0;
  background-color: white;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.about-text {
  padding-right: 40px;
}

.about-title {
  margin-bottom: 30px;
  color: #333745;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0px;
  text-transform: uppercase;
}

.about-description {
  font-size: 1rem;
  line-height: 1.8;
  color: #666;
  margin-bottom: 20px;
  font-weight: 300;
}

.about-image {
  text-align: center;
}

.businessman-img {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* About Responsive */
@media (max-width: 768px) {
  .about-section {
    padding: 80px 0;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .about-text {
    padding-right: 0;
    text-align: center;
  }

  .about-title {
    font-size: 2rem;
  }
}

/*--------------------- Why Choose Section ---------------------*/
.why-choose-section {
  padding: 100px 0;
  background-color: #f8f8f8;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 300;
  text-align: center;
  margin-bottom: 80px;
  color: #333745;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 60px;
}

.feature-item {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.feature-icon {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  background-color: #f2d03b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.feature-icon i {
  font-size: 2rem;
  color: #333745;
}

.feature-item:hover .feature-icon {
  background-color: #333745;
}

.feature-item:hover .feature-icon i {
  color: #f2d03b;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333745;
  line-height: 1.4;
}

.feature-description {
  font-size: 0.9rem;
  line-height: 1.7;
  color: #666;
  font-weight: 300;
}

/* Why Choose Responsive */
@media (max-width: 768px) {
  .why-choose-section {
    padding: 80px 0;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 60px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .feature-icon {
    margin: 0 auto;
  }
}

/*--------------------- Skills Section ---------------------*/
.skills-section {
  padding: 100px 0;
  background-color: white;
}

.skills-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.skills-text {
  padding-right: 40px;
}

.skills-title {
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 30px;
  color: #333745;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.skills-description {
  font-size: 1rem;
  line-height: 1.8;
  color: #666;
  font-weight: 300;
}

.skills-bars {
  padding-left: 40px;
}

.skill-item {
  margin-bottom: 40px;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.skill-name {
  font-size: 1rem;
  font-weight: 600;
  color: #333745;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.skill-percentage {
  font-size: 0.9rem;
  color: #f2d03b;
  font-weight: 600;
}

.skill-bar {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background-color: #f2d03b;
  border-radius: 4px;
  transition: width 2s ease-in-out;
  width: 0;
}

/* Skills Responsive */
@media (max-width: 768px) {
  .skills-section {
    padding: 80px 0;
  }

  .skills-content {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .skills-text {
    padding-right: 0;
    text-align: center;
  }

  .skills-bars {
    padding-left: 0;
  }

  .skills-title {
    font-size: 2rem;
  }
}

/*--------------------- Footer ---------------------*/
.footer {
  background-color: #333745;
  color: white;
  padding: 80px 0 0;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 60px;
  margin-bottom: 50px;
}

.footer-brand {
  padding-right: 40px;
}

.footer-logo {
  max-width: 150px;
  height: auto;
  margin-bottom: 25px;
}

.footer-description {
  font-size: 0.9rem;
  line-height: 1.7;
  color: #ccc;
  margin-bottom: 30px;
  font-weight: 300;
}

.footer-social {
  display: flex;
  gap: 15px;
}

.social-link {
  width: 40px;
  height: 40px;
  background-color: #6d6d6d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333745;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: white;
  transform: translateY(-3px);
}

.footer-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 25px;
  color: #f2d03b;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.footer-list {
  list-style: none;
  padding: 0;
}

.footer-list li {
  font-size: 0.9rem;
  line-height: 1.8;
  color: #ccc;
  margin-bottom: 8px;
  font-weight: 300;
}

.newsletter-form {
  display: flex;
  gap: 10px;
}

.newsletter-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #555;
  background-color: #444;
  color: white;
  border-radius: 3px;
  font-size: 0.9rem;
}

.newsletter-input::placeholder {
  color: #999;
}

.newsletter-btn {
  padding: 12px 15px;
  background-color: #f2d03b;
  border: none;
  border-radius: 3px;
  color: #333745;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.newsletter-btn:hover {
  background-color: #e6c235;
}

.footer-bottom {
  border-top: 1px solid #555;
  padding: 30px 0;
  text-align: center;
}

.copyright {
  font-size: 0.8rem;
  color: #999;
  margin: 0;
}

.copyright a {
  color: #f2d03b;
  text-decoration: none;
}

.copyright a:hover {
  text-decoration: underline;
}

/* Footer Responsive */
@media (max-width: 768px) {
  .footer {
    padding: 60px 0 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .footer-brand {
    padding-right: 0;
  }

  .newsletter-form {
    flex-direction: column;
    gap: 15px;
  }

  .newsletter-btn {
    align-self: center;
    width: fit-content;
    padding: 12px 25px;
  }
}
