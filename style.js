document.addEventListener("DOMContentLoaded", function () {
  // Slider functionality
  const slides = document.querySelectorAll(".slide");
  const navDots = document.querySelectorAll(".nav-dot");
  let currentSlide = 0;
  let slideInterval;
  const slideIntervalTime = 5000; // 5 seconds

  // Initialize slider only if elements exist
  if (slides.length > 0 && navDots.length > 0) {
    // Set background images
    slides.forEach((slide) => {
      const bgImage = slide.getAttribute("data-bg");
      if (bgImage) {
        slide.style.backgroundImage = `url('${bgImage}')`;
      }
    });

    // Function to show specific slide
    function showSlide(index) {
      if (index < 0 || index >= slides.length) return;

      // Remove active class from all slides and dots
      slides.forEach((slide) => slide.classList.remove("active"));
      navDots.forEach((dot) => dot.classList.remove("active"));

      // Add active class to current slide and dot
      slides[index].classList.add("active");
      if (navDots[index]) {
        navDots[index].classList.add("active");
      }

      currentSlide = index;
    }

    // Function to go to next slide
    function nextSlide() {
      const next = (currentSlide + 1) % slides.length;
      showSlide(next);
    }

    // Function to start auto-advance
    function startSlideInterval() {
      slideInterval = setInterval(nextSlide, slideIntervalTime);
    }

    // Function to stop auto-advance
    function stopSlideInterval() {
      if (slideInterval) {
        clearInterval(slideInterval);
      }
    }

    // Add click event listeners to navigation dots
    navDots.forEach((dot, index) => {
      dot.addEventListener("click", () => {
        stopSlideInterval();
        showSlide(index);
        startSlideInterval();
      });
    });

    // Pause on hover
    const sliderContainer = document.querySelector(".qode-home-slider-5");
    if (sliderContainer) {
      sliderContainer.addEventListener("mouseenter", stopSlideInterval);
      sliderContainer.addEventListener("mouseleave", startSlideInterval);
    }

    // Initialize first slide and start auto-advance
    showSlide(0);
    startSlideInterval();
  }

  // Skills progress bars animation
  const skillBars = document.querySelectorAll(".skill-progress");
  const skillsSection = document.querySelector(".skills-section");

  // Initialize skills animation only if elements exist
  if (skillBars.length > 0 && skillsSection) {
    // Intersection Observer for skills animation
    const skillsObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            skillBars.forEach((bar, index) => {
              const width = bar.getAttribute("data-width");
              if (width) {
                // Stagger the animation for each bar
                setTimeout(() => {
                  bar.style.width = width + "%";
                }, 200 * index);
              }
            });
            skillsObserver.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.3 }
    );

    skillsObserver.observe(skillsSection);
  }

  // Smooth scrolling for navigation links
  const navLinks = document.querySelectorAll('.menu__link');
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      const href = this.getAttribute('href');
      if (href && href.startsWith('#')) {
        e.preventDefault();
        const target = document.querySelector(href);
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    });
  });

  // Newsletter form handling
  const newsletterForm = document.querySelector('.newsletter-form');
  if (newsletterForm) {
    newsletterForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const emailInput = this.querySelector('.newsletter-input');
      const email = emailInput.value.trim();

      if (email && isValidEmail(email)) {
        // Here you would typically send the email to your server
        alert('Thank you for subscribing to our newsletter!');
        emailInput.value = '';
      } else {
        alert('Please enter a valid email address.');
      }
    });
  }

  // Email validation function
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
});
