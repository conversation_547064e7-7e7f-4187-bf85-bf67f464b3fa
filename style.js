document.addEventListener("DOMContentLoaded", function () {
  const slides = document.querySelectorAll(".slide");
  const navDots = document.querySelectorAll(".nav-dot");
  let currentSlide = 0;
  const slideInterval = 5000; // 5 seconds

  // Set background images
  slides.forEach((slide) => {
    const bgImage = slide.getAttribute("data-bg");
    slide.style.backgroundImage = `url('${bgImage}')`;
  });

  // Function to show specific slide
  function showSlide(index) {
    // Remove active class from all slides and dots
    slides.forEach((slide) => slide.classList.remove("active"));
    navDots.forEach((dot) => dot.classList.remove("active"));

    // Add active class to current slide and dot
    slides[index].classList.add("active");
    navDots[index].classList.add("active");

    currentSlide = index;
  }

  // Function to go to next slide
  function nextSlide() {
    const next = (currentSlide + 1) % slides.length;
    showSlide(next);
  }

  // Add click event listeners to navigation dots
  navDots.forEach((dot, index) => {
    dot.addEventListener("click", () => {
      showSlide(index);
    });
  });

  // Auto-advance slides
  setInterval(nextSlide, slideInterval);

  // Initialize first slide
  showSlide(0);

  // Skills progress bars animation
  const skillBars = document.querySelectorAll(".skill-progress");
  const skillsSection = document.querySelector(".skills-section");

  // Intersection Observer for skills animation
  const skillsObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          skillBars.forEach((bar) => {
            const width = bar.getAttribute("data-width");
            setTimeout(() => {
              bar.style.width = width + "%";
            }, 500);
          });
          skillsObserver.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.5 }
  );

  if (skillsSection) {
    skillsObserver.observe(skillsSection);
  }
});
